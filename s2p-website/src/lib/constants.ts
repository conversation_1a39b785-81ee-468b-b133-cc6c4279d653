export const COMPANY_INFO = {
  name: "S2P Infotech",
  tagline: "Empowering Businesses with Cutting-Edge IT Solutions",
  subtitle: "Your trusted partner for comprehensive technology services",
  description: "S2P Infotech is a comprehensive IT services company specializing in enterprise solutions, cybersecurity, hardware infrastructure, and cutting-edge technology implementations.",
  email: "<EMAIL>",
  phone: "+****************",
  address: "123 Technology Street, IT City, TC 12345",
};

export const SERVICES = [
  {
    title: "IT Consulting",
    description: "Strategic technology planning and implementation to optimize your business operations",
    icon: "settings",
  },
  {
    title: "Software Solutions",
    description: "Enterprise software deployment and customization for enhanced productivity",
    icon: "code",
  },
  {
    title: "Cybersecurity",
    description: "Comprehensive security solutions and monitoring to protect your digital assets",
    icon: "shield",
  },
  {
    title: "Hardware & Infrastructure",
    description: "Server, networking, and hardware solutions for robust IT infrastructure",
    icon: "server",
  },
  {
    title: "CAD/Design Solutions",
    description: "Professional design and engineering software for creative professionals",
    icon: "paintbrush",
  },
  {
    title: "Surveillance Systems",
    description: "Complete CCTV and security system integration for enhanced safety",
    icon: "eye",
  },
  {
    title: "Networking Solutions",
    description: "Advanced networking infrastructure for seamless connectivity",
    icon: "network",
  },
  {
    title: "Support & Maintenance",
    description: "24/7 technical support and system maintenance services",
    icon: "headphones",
  },
];

export const PARTNERS = [
  { name: "Adobe", logo: "/images/partners/adobe.png", category: "Software" },
  { name: "Apple", logo: "/images/partners/apple.png", category: "Hardware" },
  { name: "AutoDesk", logo: "/images/partners/autodesk.png", category: "CAD" },
  { name: "BricsCAD", logo: "/images/partners/bricsys.png", category: "CAD" },
  { name: "Cisco", logo: "/images/partners/cisco.png", category: "Networking" },
  { name: "CP Plus", logo: "/images/partners/cp_plus.png", category: "Security" },
  { name: "D-Link", logo: "/images/partners/d_link.png", category: "Networking" },
  { name: "Dell", logo: "/images/partners/dell.jpeg", category: "Hardware" },
  { name: "HP", logo: "/images/partners/hp.jpeg", category: "Hardware" },
  { name: "K7 Security", logo: "/images/partners/k7_security.png", category: "Security" },
  { name: "Microsoft", logo: "/images/partners/microsoft.png", category: "Software" },
  { name: "SketchUp", logo: "/images/partners/sketch_up.png", category: "CAD" },
  { name: "SonicWall", logo: "/images/partners/sonic_wall.png", category: "Security" },
  { name: "Sophos", logo: "/images/partners/sophos.png", category: "Security" },
  { name: "Tata Teleservices", logo: "/images/partners/tata_tele.png", category: "Networking" },
  { name: "Zoho", logo: "/images/partners/zoho.png", category: "Software" },
  { name: "ZWCAD", logo: "/images/partners/zwcad.png", category: "CAD" },
];

export const PARTNER_CATEGORIES = ["All", "Software", "Hardware", "CAD", "Security", "Networking"];

export const VALUE_PROPOSITIONS = [
  {
    title: "19+ Technology Partnerships",
    description: "Extensive network of leading technology vendors",
    icon: "handshake",
  },
  {
    title: "Enterprise-Grade Solutions",
    description: "Scalable solutions designed for enterprise environments",
    icon: "building",
  },
  {
    title: "24/7 Support & Maintenance",
    description: "Round-the-clock technical support and system maintenance",
    icon: "clock",
  },
  {
    title: "Industry Expertise",
    description: "Deep understanding of various industry requirements",
    icon: "award",
  },
];

export const NAVIGATION_ITEMS = [
  { name: "Home", href: "/" },
  { name: "About", href: "/about" },
  { name: "Services", href: "/services" },
  { name: "Solutions", href: "/solutions" },
  { name: "Partners", href: "/partners" },
  { name: "Contact", href: "/contact" },
];
