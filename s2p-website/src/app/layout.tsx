import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer-new";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "S2P Infotech - Empowering Businesses with Cutting-Edge IT Solutions",
  description: "Your trusted partner for comprehensive technology services including IT consulting, software solutions, cybersecurity, and hardware infrastructure.",
  keywords: "IT services, technology consulting, cybersecurity, software solutions, enterprise IT, hardware infrastructure, digital transformation, cloud solutions",
  authors: [{ name: "S2P Infotech" }],
  creator: "S2P Infotech",
  publisher: "S2P Infotech",
  robots: "index, follow",
  viewport: "width=device-width, initial-scale=1",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" }
  ],
  openGraph: {
    title: "S2P Infotech - Empowering Businesses with Cutting-Edge IT Solutions",
    description: "Your trusted partner for comprehensive technology services including IT consulting, software solutions, cybersecurity, and hardware infrastructure.",
    type: "website",
    locale: "en_US",
    url: "https://s2pinfotech.com",
    siteName: "S2P Infotech",
  },
  twitter: {
    card: "summary_large_image",
    title: "S2P Infotech - Empowering Businesses with Cutting-Edge IT Solutions",
    description: "Your trusted partner for comprehensive technology services",
    creator: "@s2pinfotech",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased min-h-screen bg-background text-foreground`}>
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-grow pt-20">{children}</main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
