@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

:root {
  /* Dark theme as default - Modern dark color palette */
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;
  --radius: 0.75rem;

  /* Enhanced gradients for modern dark theme */
  --gradient-primary: linear-gradient(135deg, hsl(0 0% 3.9%) 0%, hsl(0 0% 9%) 25%, hsl(0 0% 3.9%) 50%, hsl(0 0% 14.9%) 75%, hsl(0 0% 3.9%) 100%);
  --gradient-secondary: linear-gradient(45deg, hsl(0 0% 3.9%) 0%, hsl(0 0% 7%) 50%, hsl(0 0% 3.9%) 100%);
  --gradient-accent: linear-gradient(to right, hsl(0 0% 3.9%), hsl(0 0% 9%), hsl(0 0% 3.9%));
  --gradient-hero: linear-gradient(135deg, hsl(0 0% 3.9%) 0%, hsl(240 5.9% 10%) 50%, hsl(0 0% 3.9%) 100%);

  /* Chart colors for dark theme */
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

/* Light theme override (optional - we're going dark-first) */
.light {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 89.8%;
  --input: 0 0% 89.8%;
  --ring: 0 0% 3.9%;
}

body {
  font-family: var(--font-inter), system-ui, sans-serif;
  overflow-x: hidden;
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  /* Enhanced dark theme background with subtle patterns */
  background-image:
    radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(59, 130, 246, 0.04) 0%, transparent 50%);
  background-attachment: fixed;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Modern scrollbar design for dark theme */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 6px;
  border: 1px solid hsl(var(--background));
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Enhanced focus states for accessibility */
*:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: calc(var(--radius) - 2px);
}

/* Enhanced gradient backgrounds */
.gradient-dark {
  background: var(--gradient-primary);
}

.gradient-subtle {
  background: var(--gradient-secondary);
}

.gradient-radial {
  background: radial-gradient(ellipse at center, #1a1a1a 0%, #000000 70%);
}

.gradient-mesh {
  background: 
    radial-gradient(circle at 25% 25%, #1a1a1a 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, #0f0f0f 0%, transparent 50%),
    linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
}

/* Modern glassmorphism effects for dark theme */
.glass {
  background: hsl(var(--background) / 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid hsl(var(--border) / 0.5);
  box-shadow:
    0 8px 32px hsl(var(--background) / 0.8),
    inset 0 1px 0 hsl(var(--foreground) / 0.1);
}

.glass-strong {
  background: hsl(var(--card) / 0.9);
  backdrop-filter: blur(30px);
  border: 1px solid hsl(var(--border));
  box-shadow:
    0 16px 64px hsl(var(--background) / 0.9),
    inset 0 1px 0 hsl(var(--foreground) / 0.15);
}

/* Enhanced animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-8px) rotate(1deg); }
  50% { transform: translateY(-15px) rotate(0deg); }
  75% { transform: translateY(-8px) rotate(-1deg); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
}

@keyframes pulse-soft {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.animate-pulse-soft {
  animation: pulse-soft 3s ease-in-out infinite;
}

/* Modern button styles */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Modern card hover effects */
.card-modern {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.card-modern::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card-modern:hover::before {
  opacity: 1;
}

/* Enhanced text gradient utilities for dark theme */
.text-gradient {
  background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 50%, hsl(var(--foreground)) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-subtle {
  background: linear-gradient(135deg, hsl(var(--foreground)) 0%, hsl(var(--muted-foreground)) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-brand {
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #3b82f6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Loading states */
@keyframes skeleton {
  0% { opacity: 0.4; }
  50% { opacity: 0.8; }
  100% { opacity: 0.4; }
}

.animate-skeleton {
  animation: skeleton 2s ease-in-out infinite;
  background: linear-gradient(90deg, #1a1a1a, #2a2a2a, #1a1a1a);
  background-size: 200% 100%;
}

/* Animation delays */
.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-600 {
  animation-delay: 600ms;
}

.animation-delay-1000 {
  animation-delay: 1000ms;
}

.animation-delay-1500 {
  animation-delay: 1500ms;
}

.animation-delay-2000 {
  animation-delay: 2000ms;
}

/* Responsive text sizes */
@media (max-width: 640px) {
  .text-responsive-hero {
    font-size: 2.5rem;
    line-height: 1.1;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .text-responsive-hero {
    font-size: 4rem;
    line-height: 1.1;
  }
}

@media (min-width: 1025px) {
  .text-responsive-hero {
    font-size: 6rem;
    line-height: 1.1;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Dark theme class (redundant since we're dark-first, but kept for compatibility) */
.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 0 0% 83.1%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
