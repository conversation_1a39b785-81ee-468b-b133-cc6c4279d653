'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Mail,
  Phone,
  MapPin,
  ArrowRight,
  ExternalLink,
  Globe,
  MessageCircle,
  Share2
} from 'lucide-react';
import { COMPANY_INFO } from '@/lib/constants';

const socialLinks = [
  { name: 'LinkedIn', href: '#', icon: Globe },
  { name: 'Twitter', href: '#', icon: MessageCircle },
  { name: 'Facebook', href: '#', icon: Share2 },
];

const quickLinks = [
  { name: 'About Us', href: '/about' },
  { name: 'Services', href: '/services' },
  { name: 'Solutions', href: '/solutions' },
  { name: 'Partners', href: '/partners' },
  { name: 'Contact', href: '/contact' },
];

const services = [
  { name: 'IT Consulting', href: '/services' },
  { name: 'Software Development', href: '/services' },
  { name: 'Cybersecurity', href: '/services' },
  { name: 'Cloud Solutions', href: '/services' },
  { name: 'Hardware Infrastructure', href: '/services' },
];

export default function Footer() {
  return (
    <footer className="relative border-t border-border/50 overflow-hidden bg-background">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-t from-muted/20 to-background -z-10"></div>
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,rgba(255,255,255,0.1),rgba(255,255,255,0))] opacity-20 -z-10"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-3 mb-6 group">
              <div className="relative">
                <Image
                  src="/images/logo.svg"
                  alt={COMPANY_INFO.name}
                  width={40}
                  height={40}
                  className="w-10 h-10 transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              <span className="text-xl font-bold text-gradient-brand">
                {COMPANY_INFO.name}
              </span>
            </Link>

            <p className="text-muted-foreground mb-6 leading-relaxed">
              {COMPANY_INFO.description}
            </p>

            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                <Mail className="h-4 w-4 text-primary" />
                <span>{COMPANY_INFO.email}</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                <Phone className="h-4 w-4 text-primary" />
                <span>{COMPANY_INFO.phone}</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4 text-primary" />
                <span>{COMPANY_INFO.address}</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-foreground mb-6">Quick Links</h3>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm group flex items-center"
                  >
                    {link.name}
                    <ArrowRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="font-semibold text-foreground mb-6">Our Services</h3>
            <ul className="space-y-3">
              {services.map((service) => (
                <li key={service.name}>
                  <Link
                    href={service.href}
                    className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm group flex items-center"
                  >
                    {service.name}
                    <ArrowRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter & Social */}
          <div>
            <h3 className="font-semibold text-foreground mb-6">Stay Connected</h3>

            <Card className="glass border-border/50 mb-6">
              <CardContent className="p-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Subscribe to our newsletter for the latest updates and insights.
                </p>
                <Button asChild className="w-full bg-primary text-primary-foreground hover:bg-primary/90">
                  <Link href="/contact">
                    Subscribe
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <div>
              <p className="text-sm text-muted-foreground mb-4">Follow us on social media</p>
              <div className="flex space-x-3">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon;
                  return (
                    <Button
                      key={social.name}
                      variant="outline"
                      size="icon"
                      asChild
                      className="border-border/50 hover:border-border hover:bg-accent/50"
                    >
                      <Link href={social.href} aria-label={social.name}>
                        <IconComponent className="h-4 w-4" />
                      </Link>
                    </Button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        <Separator className="mb-8 bg-border/50" />

        {/* Bottom section */}
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
            <p className="text-sm text-muted-foreground">
              © {new Date().getFullYear()} {COMPANY_INFO.name}. All rights reserved.
            </p>
            <div className="flex space-x-4">
              <Link href="/privacy" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="bg-background/50 border-border/50">
              Made with ❤️ by S2P Infotech
            </Badge>
          </div>
        </div>
      </div>
    </footer>
  );
}