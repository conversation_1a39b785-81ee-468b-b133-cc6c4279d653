'use client';

import { useState } from 'react';
import {
  Settings,
  Code,
  Shield,
  Server,
  Paintbrush,
  Eye,
  Network,
  Headphones,
  ArrowRight
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { motion } from 'framer-motion';
import { SERVICES } from '@/lib/constants';
import Link from 'next/link';

const iconMap = {
  settings: Settings,
  code: Code,
  shield: Shield,
  server: Server,
  paintbrush: Paintbrush,
  eye: Eye,
  network: Network,
  headphones: Headphones,
};

const serviceCategories = [
  { id: 'all', label: 'All Services' },
  { id: 'consulting', label: 'Consulting' },
  { id: 'software', label: 'Software' },
  { id: 'hardware', label: 'Hardware' },
];

export default function ServicesSection() {
  const [activeCategory, setActiveCategory] = useState('all');

  // Filter services based on active category
  const filteredServices = activeCategory === 'all'
    ? SERVICES
    : SERVICES.filter(service => {
        if (activeCategory === 'consulting' && (service.title.includes('Consulting') || service.title.includes('IT Consulting')))
          return true;
        if (activeCategory === 'software' && (service.title.includes('Software') || service.title.includes('CAD')))
          return true;
        if (activeCategory === 'hardware' && (service.title.includes('Hardware') || service.title.includes('Infrastructure') || service.title.includes('Networking') || service.title.includes('Surveillance')))
          return true;
        return false;
      });

  return (
    <section className="py-24 relative overflow-hidden bg-background">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-background to-muted/20 -z-10"></div>
      {/* Modern grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10 -z-10"></div>
      {/* Gradient orbs */}
      <div className="absolute top-10 left-10 w-[500px] h-[300px] bg-blue-500/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-10 right-10 w-[500px] h-[300px] bg-purple-500/5 rounded-full blur-3xl -z-10"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <Badge variant="outline" className="mb-4 bg-background/50 border-border">
              Our Services
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight text-gradient">
              Comprehensive IT Solutions
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              From strategic consulting to cutting-edge implementation, we deliver end-to-end technology solutions that drive business growth.
            </p>
          </motion.div>
        </div>

        {/* Services Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="mb-12"
        >
          <Tabs defaultValue="all" className="w-full" onValueChange={setActiveCategory}>
            <TabsList className="grid grid-cols-2 md:grid-cols-4 w-full mb-12 bg-muted/50">
              {serviceCategories.map(category => (
                <TabsTrigger
                  key={category.id}
                  value={category.id}
                  className="data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
                >
                  {category.label}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={activeCategory} className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredServices.map((service, index) => {
                  const IconComponent = iconMap[service.icon as keyof typeof iconMap] || Settings;

                  return (
                    <motion.div
                      key={service.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <Card className="card-modern glass border-border/50 hover:border-border transition-all duration-300 h-full flex flex-col group hover:shadow-lg hover:shadow-primary/5">
                        <CardHeader className="pb-4">
                          <div className="w-14 h-14 rounded-xl bg-muted/50 flex items-center justify-center mb-4 group-hover:bg-primary/10 transition-colors duration-300">
                            <IconComponent className="h-6 w-6 text-primary" />
                          </div>
                          <CardTitle className="text-xl text-foreground">{service.title}</CardTitle>
                        </CardHeader>
                        <CardContent className="text-muted-foreground text-sm flex-1">
                          <CardDescription className="text-base leading-relaxed">
                            {service.description}
                          </CardDescription>
                        </CardContent>
                        <CardFooter className="pt-4">
                          <Button asChild variant="ghost" className="group/btn w-full justify-between hover:bg-accent/50">
                            <Link href="/services">
                              Learn More
                              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                            </Link>
                          </Button>
                        </CardFooter>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.4 }}
          className="text-center mt-16"
        >
          <Card className="glass-strong border-border/50 max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-semibold mb-4 text-foreground">
                Ready to Transform Your Business?
              </h3>
              <p className="text-muted-foreground mb-6 text-lg">
                Let&apos;s discuss how our comprehensive IT solutions can drive your business forward.
              </p>
              <Button asChild size="lg" className="rounded-full px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Link href="/services">
                  Explore All Services
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}