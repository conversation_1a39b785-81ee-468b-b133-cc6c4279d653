'use client';

import { useState } from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { PARTNERS, PARTNER_CATEGORIES } from '@/lib/constants';

export default function PartnersSection() {
  const [activeCategory, setActiveCategory] = useState('All');

  // Filter partners based on active category
  const filteredPartners = activeCategory === 'All'
    ? PARTNERS
    : PARTNERS.filter(partner => partner.category === activeCategory);

  return (
    <section className="py-24 relative overflow-hidden bg-background">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-background to-muted/10 -z-10"></div>
      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-5 -z-10"></div>
      {/* Gradient orbs */}
      <div className="absolute top-20 left-20 w-[400px] h-[200px] bg-blue-500/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-20 right-20 w-[400px] h-[200px] bg-purple-500/5 rounded-full blur-3xl -z-10"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7 }}
          className="text-center mb-16"
        >
          <Badge variant="outline" className="mb-4 bg-background/50 border-border">
            Technology Partners
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4 tracking-tight text-gradient">
            Trusted Technology Partnerships
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            We collaborate with industry-leading technology providers to deliver best-in-class solutions and ensure our clients have access to cutting-edge innovations.
          </p>
        </motion.div>

        {/* Partners Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.2 }}
        >
          <Tabs defaultValue="All" className="w-full" onValueChange={setActiveCategory}>
            <TabsList className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 w-full mb-12 bg-muted/50">
              {PARTNER_CATEGORIES.map(category => (
                <TabsTrigger
                  key={category}
                  value={category}
                  className="data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm text-xs md:text-sm"
                >
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={activeCategory} className="mt-0">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {filteredPartners.map((partner, index) => (
                  <motion.div
                    key={partner.name}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="group"
                  >
                    <Card className="h-36 p-4 flex flex-col items-center justify-center gap-2 glass border-border/50 hover:border-border hover:shadow-lg hover:shadow-primary/5 transition-all duration-300 group">
                      <div className="relative w-20 h-20 flex items-center justify-center">
                        <Image
                          src={partner.logo}
                          alt={partner.name}
                          width={80}
                          height={80}
                          className="max-w-full max-h-full object-contain opacity-70 group-hover:opacity-100 transition-opacity duration-300 filter brightness-0 invert dark:brightness-100 dark:invert-0"
                        />
                      </div>
                      <Badge variant="outline" className="bg-transparent border-border/50 text-xs font-normal">
                        {partner.category}
                      </Badge>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Mobile Featured Partners Carousel */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.4 }}
          className="mt-16 lg:hidden"
        >
          <div className="text-center mb-8">
            <h3 className="text-xl font-medium mb-2 text-foreground">Featured Partners</h3>
            <p className="text-sm text-muted-foreground">Swipe to see our key technology partners</p>
          </div>

          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            className="w-full"
          >
            <CarouselContent>
              {PARTNERS.filter((_, i) => i < 10).map((partner) => (
                <CarouselItem key={partner.name} className="md:basis-1/2 lg:basis-1/3 pl-4">
                  <Card className="h-36 p-4 flex flex-col items-center justify-center gap-2 glass border-border/50">
                    <div className="relative w-20 h-20 flex items-center justify-center">
                      <Image
                        src={partner.logo}
                        alt={partner.name}
                        width={80}
                        height={80}
                        className="max-w-full max-h-full object-contain opacity-70 filter brightness-0 invert dark:brightness-100 dark:invert-0"
                      />
                    </div>
                    <Badge variant="outline" className="bg-transparent border-border/50 text-xs font-normal">
                      {partner.category}
                    </Badge>
                  </Card>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-2" />
            <CarouselNext className="right-2" />
          </Carousel>
        </motion.div>

        {/* Partnership Benefits */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.7, delay: 0.6 }}
          className="mt-20"
        >
          <Card className="glass-strong border-border/50 max-w-4xl mx-auto">
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                  <div className="text-3xl font-bold text-primary mb-2">19+</div>
                  <div className="text-sm text-muted-foreground">Technology Partners</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-primary mb-2">100%</div>
                  <div className="text-sm text-muted-foreground">Certified Solutions</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                  <div className="text-sm text-muted-foreground">Partner Support</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}